name: "✨ Feature Request"
description: "Suggest a new feature or improvement for the Arabiyy keyboard"
title: "Feature: "
labels: ["enhancement"]
assignees: []

body:
  - type: textarea
    id: feature-problem
    attributes:
      label: "Is your feature request related to a problem? Please describe."
      description: "A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]"
    validations:
      required: true

  - type: textarea
    id: feature-solution
    attributes:
      label: "Describe the solution you'd like"
      description: "A clear and concise description of what you want to happen."
    validations:
      required: true

  - type: textarea
    id: feature-alternatives
    attributes:
      label: "Describe alternatives you've considered"
      description: "A clear and concise description of any alternative solutions or features you've considered."
    validations:
      required: false

  - type: checkboxes
    id: layout-impact
    attributes:
      label: "Keyboard Layout Impact"
      description: "Does this feature require changes to any of the following?"
      options:
        - label: This feature requires changes to the keyboard layout
        - label: This feature requires changes to the keyboard rules
        - label: This feature requires changes to the keyboard documentation

  - type: textarea
    id: additional-context
    attributes:
      label: "Additional context"
      description: "Add any other context or screenshots about the feature request here."
      placeholder: "Context, screenshots, or related issues." 